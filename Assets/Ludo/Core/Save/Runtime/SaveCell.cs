﻿using UnityEngine;

namespace Ludo.Core.Save
{
    [System.Serializable]
    public class SaveCell
    {
        [SerializeField] int hash;
        [SerializeField] string json;
        [System.NonSerialized] ISave _save;

        public int Hash => hash;
        public bool IsReassembled { get; set; }
        public ISave Save => _save;

        public SaveCell(int hash, ISave save)
        {
            this.hash = hash;
            _save = save;
            IsReassembled = true;
        }

        public void SetSave(ISave save)
        {
            _save = save;
        }

        public void Flush()
        {
            _save?.Flush();
            if (IsReassembled) json = JsonUtility.ToJson(_save);
        }

        public void Reconstruct<T>() where T : ISave
        {
            _save = JsonUtility.FromJson<T>(json);
            IsReassembled = true;
        }
    }
}