%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &545691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 545694}
  - component: {fileID: 545693}
  - component: {fileID: 545692}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &545692
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545691}
  m_Enabled: 1
--- !u!20 &545693
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545691}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &545694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545691}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5306505
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5306506}
  m_Layer: 5
  m_Name: UISpriteSpring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5306506
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5306505}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 708706224}
  m_Father: {fileID: 446178078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 57.38611}
  m_SizeDelta: {x: 0, y: 114.7722}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &19892037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 19892038}
  - component: {fileID: 19892040}
  - component: {fileID: 19892039}
  - component: {fileID: 19892042}
  - component: {fileID: 19892041}
  m_Layer: 5
  m_Name: Main
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &19892038
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 19892037}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2029185233}
  m_Father: {fileID: 862145651}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &19892039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 19892037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.12941177, g: 0.6509804, b: 0.88235295, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &19892040
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 19892037}
  m_CullTransparentMesh: 1
--- !u!114 &19892041
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 19892037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 19892038}
  useTransformAsTarget: 1
  targetTransform: {fileID: 2027155075}
--- !u!114 &19892042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 19892037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2efc2ceed5291441491b48487ae12a68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  anchoredPositionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
  useTransformAsTarget: 1
  followRectTransform: {fileID: 19892038}
  targetRectTransform: {fileID: 2027155075}
  anchoredPositionTarget: {x: 0, y: 0}
--- !u!1 &60979578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 60979579}
  - component: {fileID: 60979581}
  - component: {fileID: 60979580}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &60979579
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 60979578}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1004947795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: -20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &60979580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 60979578}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: NOTIFICATION
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 1d3fbda22bde544f9bd99d0a3c961fdc, type: 2}
  m_sharedMaterial: {fileID: -3115547586284834249, guid: 1d3fbda22bde544f9bd99d0a3c961fdc, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 36
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &60979581
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 60979578}
  m_CullTransparentMesh: 1
--- !u!1 &240082871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 240082872}
  - component: {fileID: 240082873}
  m_Layer: 5
  m_Name: UISpringDemo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &240082872
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240082871}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 862145651}
  m_Father: {fileID: 446178078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &240082873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240082871}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78371af429f547769890277b9bcb775c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  moveTargetEvery: 1.5
  container: {fileID: 862145651}
  target: {fileID: 2027155075}
  targetTransformSpring: {fileID: 2027155079}
  targetColorSpring: {fileID: 2027155078}
--- !u!1 &257557167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 257557168}
  m_Layer: 0
  m_Name: RightPosition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &257557168
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 257557167}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.87, y: -3.74, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2077441563}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &429775167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 429775168}
  - component: {fileID: 429775170}
  - component: {fileID: 429775169}
  m_Layer: 5
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &429775168
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429775167}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2113214516}
  m_Father: {fileID: 1079387714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 40.0453, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &429775169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429775167}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19344072, g: 0.590372, b: 0.5943396, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: a02d128e5c723426794a121e97682673, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &429775170
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429775167}
  m_CullTransparentMesh: 1
--- !u!1 &429826841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 429826842}
  - component: {fileID: 429826845}
  - component: {fileID: 429826844}
  - component: {fileID: 429826843}
  m_Layer: 5
  m_Name: Healthbar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &429826842
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429826841}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: **********}
  - {fileID: **********}
  m_Father: {fileID: 534543776}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 269}
  m_SizeDelta: {x: 500, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &429826843
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429826841}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b326b6ad798743c8b25fc018b43d5843, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mainBar: {fileID: **********}
  delayedBar: {fileID: **********}
  delayedBarForce: 5
  delayedBarDrag: 4
  increasingMainBarForce: 10
  increasingMainBarDrag: 5
  useGradient: 1
  healthGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0, b: 0, a: 1}
    key1: {r: 0.6901961, g: 0, b: 0.6180711, a: 1}
    key2: {r: 0.1728818, g: 0.21969502, b: 0.990566, a: 0}
    key3: {r: 0.034784317, g: 1, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 21010
    ctime2: 41249
    ctime3: 65535
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: 0
    m_NumColorKeys: 4
    m_NumAlphaKeys: 2
  mainBarColor: {r: 0, g: 1, b: 0, a: 1}
  decreasingMainBarColor: {r: 1, g: 0, b: 0, a: 1}
  delayedBarDecreasingColor: {r: 0.8, g: 0.5019608, b: 0.5187652, a: 1}
  delayedBarIncreasingColor: {r: 0.7, g: 0.9, b: 0.7, a: 1}
  useSpringColorTransitions: 1
  healthRatio: 1
  maxHealth: 100
  minHealth: 0
  animateHealthIncrease: 1
  animateHealthDecrease: 1
--- !u!114 &429826844
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429826841}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.51530796, g: 0.5278193, b: 0.5660378, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &429826845
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 429826841}
  m_CullTransparentMesh: 1
--- !u!1 &434379234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 434379236}
  - component: {fileID: 434379235}
  m_Layer: 0
  m_Name: SceneContext
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &434379235
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434379234}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33979bcb679b4e78b4af882f0f0e7dd6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneInstallers: []
--- !u!4 &434379236
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434379234}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.87, y: -3.7399998, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &446178074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 446178078}
  - component: {fileID: 446178077}
  - component: {fileID: 446178076}
  - component: {fileID: 446178075}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &446178075
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 446178074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &446178076
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 446178074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &446178077
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 446178074}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &446178078
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 446178074}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 240082872}
  - {fileID: 5306506}
  - {fileID: 858908027}
  - {fileID: 534543776}
  - {fileID: 1004947795}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &523556385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 523556386}
  - component: {fileID: 523556387}
  m_Layer: 0
  m_Name: Square
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &523556386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 523556385}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.2, y: 0.277, z: 0}
  m_LocalScale: {x: 0.2841, y: 0.27071378, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1041978083}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &523556387
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 523556385}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 7482667652216324306, guid: 311925a002f4447b3a28927169b83ea6, type: 3}
  m_Color: {r: 0.16037738, g: 0.13692597, b: 0.13692597, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &534543775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 534543776}
  m_Layer: 5
  m_Name: UIHealthBarDemo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &534543776
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 534543775}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 429826842}
  m_Father: {fileID: 446178078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &708706223
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 708706224}
  - component: {fileID: 708706225}
  m_Layer: 5
  m_Name: Slider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &708706224
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 708706223}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1537785526}
  - {fileID: 738975275}
  - {fileID: 1079387714}
  m_Father: {fileID: 5306506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 400, y: 40}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &708706225
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 708706223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 429775169}
  m_FillRect: {fileID: 2037020085}
  m_HandleRect: {fileID: 429775168}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &738204435
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 738204436}
  m_Layer: 5
  m_Name: UISpringPopupDemo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &738204436
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 738204435}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 960, y: 540}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &738975274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 738975275}
  m_Layer: 5
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &738975275
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 738975274}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2037020085}
  m_Father: {fileID: 708706224}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -4.9999847, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &759213494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 759213495}
  - component: {fileID: 759213497}
  - component: {fileID: 759213496}
  m_Layer: 5
  m_Name: Label
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &759213495
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 759213494}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1523007159}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 5.0543}
  m_SizeDelta: {x: 333.4321, y: 39.8915}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &759213496
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 759213494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Animated Button
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_sharedMaterial: {fileID: 9026158724066135517, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4286169732
  m_fontColor: {r: 0.5171945, g: 0.759, b: 0.474375, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 31.3
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &759213497
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 759213494}
  m_CullTransparentMesh: 1
--- !u!1 &858908026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 858908027}
  m_Layer: 5
  m_Name: HealtbarSpring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &858908027
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858908026}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 446178078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &862145650
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 862145651}
  - component: {fileID: 862145653}
  - component: {fileID: 862145652}
  m_Layer: 5
  m_Name: UISpringContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &862145651
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 862145650}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2027155075}
  - {fileID: 19892038}
  - {fileID: 1523007159}
  m_Father: {fileID: 240082872}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1145.4456, y: 247.83722}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &862145652
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 862145650}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.02745098}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &862145653
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 862145650}
  m_CullTransparentMesh: 1
--- !u!1 &1004947794
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1004947795}
  - component: {fileID: 1004947799}
  - component: {fileID: 1004947798}
  - component: {fileID: 1004947796}
  - component: {fileID: 1004947797}
  - component: {fileID: 1004947800}
  m_Layer: 5
  m_Name: UINotificationPopup
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1004947795
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 60979579}
  m_Father: {fileID: 446178078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -292, y: 137}
  m_SizeDelta: {x: 500, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1004947796
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a52e29b00e184e2191226824e1e6c702, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  currentState: 0
  animationType: 1
  preset: 1
  showOnEnable: 1
  hideOnDisable: 1
  showDelay: 0
  hideDelay: 0
  autoHideDuration: 0
  showForce: 120
  showDrag: 9
  hideForce: 100
  hideDrag: 12
  addShowVelocity: 1
  addHideVelocity: 0
  showVelocityMultiplier: 1.5
  hideVelocityMultiplier: 0.5
  useScaleAnimation: 0
  hiddenScale: {x: 0, y: 0, z: 0}
  visibleScale: {x: 1, y: 1, z: 1}
  scaleOvershoot: 1.15
  usePositionAnimation: 1
  hiddenPositionOffset: {x: 0, y: -300, z: 0}
  visiblePosition: {x: 0, y: 0, z: 0}
  useRotationAnimation: 0
  hiddenRotation: {x: 0, y: 0, z: -90}
  visibleRotation: {x: 0, y: 0, z: 0}
  rotationOvershoot: 15
  onShow:
    m_PersistentCalls:
      m_Calls: []
  onShown:
    m_PersistentCalls:
      m_Calls: []
  onHide:
    m_PersistentCalls:
      m_Calls: []
  onHidden:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1004947797
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 1
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 1004947795}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!114 &1004947798
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.24528301, g: 0.24528301, b: 0.24528301, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1004947799
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_CullTransparentMesh: 1
--- !u!114 &1004947800
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1004947794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2efc2ceed5291441491b48487ae12a68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  anchoredPositionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  useTransformAsTarget: 0
  followRectTransform: {fileID: 1004947795}
  targetRectTransform: {fileID: 0}
  anchoredPositionTarget: {x: 0, y: 0}
--- !u!1 &1014071607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: 1014071610}
  - component: {fileID: **********}
  m_Layer: 5
  m_Name: DelayedBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &**********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1014071607}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429826842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 500, y: 48}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1014071607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.5, g: 0.8, b: 0.5, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: a02d128e5c723426794a121e97682673, type: 3}
  m_Type: 3
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 0
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1014071610
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1014071607}
  m_CullTransparentMesh: 1
--- !u!1 &1041978080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1041978083}
  - component: {fileID: 1041978082}
  - component: {fileID: 1041978081}
  m_Layer: 0
  m_Name: JohnTheRect
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1041978081
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041978080}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
    axisRestriction: 3
  followerTransform: {fileID: 1041978083}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!212 &1041978082
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041978080}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 7482667652216324306, guid: 311925a002f4447b3a28927169b83ea6, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &1041978083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041978080}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.87, y: -3.74, z: 0}
  m_LocalScale: {x: 0.5, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 523556386}
  - {fileID: 1109620260}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1079387713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1079387714}
  m_Layer: 5
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1079387714
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1079387713}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 429775168}
  m_Father: {fileID: 708706224}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1109620259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1109620260}
  - component: {fileID: 1109620261}
  m_Layer: 0
  m_Name: Square_001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1109620260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109620259}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.2, y: 0.277, z: 0}
  m_LocalScale: {x: 0.2841, y: 0.27071378, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1041978083}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &1109620261
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109620259}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 7482667652216324306, guid: 311925a002f4447b3a28927169b83ea6, type: 3}
  m_Color: {r: 0.16037738, g: 0.13692597, b: 0.13692597, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &1372372600
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1372372601}
  m_Layer: 0
  m_Name: LeftPosition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1372372601
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372372600}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.87, y: -3.74, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2077441563}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1381597590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: 1381597593}
  - component: {fileID: **********}
  m_Layer: 5
  m_Name: MainBar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &**********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381597590}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429826842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 500, y: 48}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381597590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.3023318, g: 0.71180683, b: 0.8113208, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: a02d128e5c723426794a121e97682673, type: 3}
  m_Type: 3
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 0
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1381597593
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381597590}
  m_CullTransparentMesh: 1
--- !u!1 &1421062468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1421062471}
  - component: {fileID: 1421062470}
  - component: {fileID: 1421062469}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1421062469
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1421062468}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
  m_ScrollDeltaPerTick: 6
--- !u!114 &1421062470
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1421062468}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1421062471
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1421062468}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1523007158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1523007159}
  - component: {fileID: 1523007161}
  - component: {fileID: 1523007160}
  - component: {fileID: 1523007162}
  m_Layer: 5
  m_Name: Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1523007159
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1523007158}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2006507497}
  - {fileID: 759213495}
  m_Father: {fileID: 862145651}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -264.2}
  m_SizeDelta: {x: 376.0174, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1523007160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1523007158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 1523007159}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!114 &1523007161
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1523007158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 739c6edf08c4641a2beb68a07b2046d7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  onClickEvent:
    m_PersistentCalls:
      m_Calls: []
  transformSpringComponent: {fileID: 1523007160}
  doScaleSpring: 1
  highlightedScale: {x: 1.1, y: 1.1, z: 1.1}
  pressedScaleVelocityAdd: {x: 5, y: 5, z: 0}
  doRotationSpring: 1
  highlightedRotation: {x: 0, y: 0, z: 1}
  pressedRotationVelocityAdd: {x: 0, y: 0, z: 3}
--- !u!114 &1523007162
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1523007158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc8e30f0f10945909652eecc95ac7081, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  springComponent: {fileID: 1523007160}
  autoStart: 1
  loop: 1
  baseScale: {x: 1, y: 1, z: 1}
  pulseScale: {x: 1.2, y: 1.2, z: 1.2}
  pulseInterval: 2.5
  randomVariation: 0.1
  springForce: 70
  springDrag: 7
  addVelocity: 1
  velocityScale: 0.5
--- !u!1 &1537785525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1537785526}
  - component: {fileID: 1537785528}
  - component: {fileID: 1537785527}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1537785526
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1537785525}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 708706224}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1537785527
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1537785525}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1537785528
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1537785525}
  m_CullTransparentMesh: 1
--- !u!1 &1922637357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1922637358}
  - component: {fileID: 1922637360}
  - component: {fileID: 1922637359}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1922637358
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1922637357}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2027155075}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1922637359
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1922637357}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: TARGET
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 25.85
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 44.583496, y: 0, z: 43.892334, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1922637360
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1922637357}
  m_CullTransparentMesh: 1
--- !u!1 &2006507496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2006507497}
  - component: {fileID: 2006507499}
  - component: {fileID: 2006507498}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2006507497
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006507496}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1523007159}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 389.3436, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2006507498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006507496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d8c2ebd4f9119472ab01024f737131cd, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2006507499
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2006507496}
  m_CullTransparentMesh: 1
--- !u!1 &2027155074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2027155075}
  - component: {fileID: 2027155077}
  - component: {fileID: 2027155076}
  - component: {fileID: 2027155079}
  - component: {fileID: 2027155078}
  m_Layer: 5
  m_Name: Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2027155075
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027155074}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1922637358}
  m_Father: {fileID: 862145651}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 120, y: 120}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2027155076
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027155074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.8396226, g: 0.24477871, b: 0.18614274, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2027155077
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027155074}
  m_CullTransparentMesh: 1
--- !u!114 &2027155078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027155074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b0e36307ce1a94f8c9502af754970c6e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 1
  colorSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 1
    springValues:
    - initialValue: 0
      clampTarget: 1
      clampCurrentValue: 1
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 1
      update: 1
      force: 150
      drag: 10
      target: 1
      currentValue: 1
      candidateValue: 1
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 1
      clampCurrentValue: 1
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 1
      update: 1
      force: 150
      drag: 10
      target: 1
      currentValue: 1
      candidateValue: 1
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 1
      clampCurrentValue: 1
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 1
      update: 1
      force: 150
      drag: 10
      target: 1
      currentValue: 1
      candidateValue: 1
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 1
      clampCurrentValue: 1
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 1
      update: 1
      force: 150
      drag: 10
      target: 1
      currentValue: 1
      candidateValue: 1
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
  autoUpdate: 1
  autoUpdatedObjectIsRenderer: 0
  autoUpdatedRenderer: {fileID: 0}
  autoUpdatedUiGraphic: {fileID: 2027155076}
--- !u!114 &2027155079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027155074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 2027155075}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!1 &2029185232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2029185233}
  - component: {fileID: 2029185235}
  - component: {fileID: 2029185234}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2029185233
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2029185232}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 19892038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2029185234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2029185232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: UI
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 44.75
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 49.76758, y: 0, z: 49.76764, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &2029185235
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2029185232}
  m_CullTransparentMesh: 1
--- !u!1 &2037020084
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2037020085}
  - component: {fileID: 2037020087}
  - component: {fileID: 2037020086}
  m_Layer: 5
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2037020085
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037020084}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 738975275}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2037020086
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037020084}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.07075471, g: 0.82945484, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2037020087
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037020084}
  m_CullTransparentMesh: 1
--- !u!1 &2077441562
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2077441563}
  - component: {fileID: 2077441564}
  m_Layer: 0
  m_Name: SpriteSpringController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2077441563
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2077441562}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1372372601}
  - {fileID: 257557168}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2077441564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2077441562}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78d454e7e0334cb8a7f56551981e7c4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slider: {fileID: 708706225}
  transformSpring: {fileID: 1041978081}
  valueText: {fileID: 2113214517}
  valueTransformSpring: {fileID: 2113214521}
  valueFloatSpring: {fileID: 2113214519}
  leftPosition: {fileID: 1372372601}
  rightPosition: {fileID: 257557168}
  tiltSensitivity: -0.5
  maxTiltAngle: 15
  velocityThreshold: 0.1
--- !u!1 &2113214515
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2113214516}
  - component: {fileID: 2113214518}
  - component: {fileID: 2113214517}
  - component: {fileID: 2113214520}
  - component: {fileID: 2113214521}
  - component: {fileID: 2113214519}
  m_Layer: 5
  m_Name: ValueText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2113214516
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429775168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 37.8}
  m_SizeDelta: {x: 50.006, y: 23.3882}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2113214517
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 0.00
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 1d3fbda22bde544f9bd99d0a3c961fdc, type: 2}
  m_sharedMaterial: {fileID: -3115547586284834249, guid: 1d3fbda22bde544f9bd99d0a3c961fdc, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 23.5
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &2113214518
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_CullTransparentMesh: 1
--- !u!114 &2113214519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f13d4c2cb1c5545fa9148718d4b06c39, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  springFloat:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 1
    springValues:
    - initialValue: 0
      clampTarget: 1
      clampCurrentValue: 1
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 1
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
--- !u!114 &2113214520
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2efc2ceed5291441491b48487ae12a68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  anchoredPositionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  useTransformAsTarget: 0
  followRectTransform: {fileID: 2113214516}
  targetRectTransform: {fileID: 0}
  anchoredPositionTarget: {x: 0, y: 0}
--- !u!114 &2113214521
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113214515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 2113214516}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 545694}
  - {fileID: 446178078}
  - {fileID: 1421062471}
  - {fileID: 1041978083}
  - {fileID: 2077441563}
  - {fileID: 434379236}
  - {fileID: 738204436}
