fileFormatVersion: 2
guid: ea4dc12e88b6e4953b0aea9b32d8b355
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6507723212533834303
    second: Explosion 1_0
  - first:
      213: 239120857569813423
    second: Explosion 1_1
  - first:
      213: 3192016352273360724
    second: Explosion 1_2
  - first:
      213: 1781890793491996108
    second: Explosion 1_3
  - first:
      213: 6157017127612718517
    second: Explosion 1_4
  - first:
      213: -1437649512377084250
    second: Explosion 1_5
  - first:
      213: 4680189953316298680
    second: Explosion 1_6
  - first:
      213: -5866917773216416411
    second: Explosion 1_7
  - first:
      213: -3949029189992728629
    second: Explosion 1_8
  - first:
      213: -6022996679288257285
    second: Explosion 1_9
  - first:
      213: -9220572818898122421
    second: Explosion 1_10
  - first:
      213: 566132404704053414
    second: Explosion 1_11
  - first:
      213: -8601460724912457990
    second: Explosion 1_12
  - first:
      213: 6104490261414907700
    second: Explosion 1_13
  - first:
      213: 8470416662218553080
    second: Explosion 1_14
  - first:
      213: -7823824540451536112
    second: Explosion 1_15
  - first:
      213: 5468580901630721627
    second: Explosion 1_16
  - first:
      213: 3363409571497986954
    second: Explosion 1_17
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: iOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Explosion 1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f32a4ab48c3105a50800000000000000
      internalID: 6507723212533834303
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_1
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa3e5dd2727815300800000000000000
      internalID: 239120857569813423
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_2
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 457ed7ad5f15c4c20800000000000000
      internalID: 3192016352273360724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_3
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ccd11ae5c3c8ab810800000000000000
      internalID: 1781890793491996108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bd1a5fc47e127550800000000000000
      internalID: 6157017127612718517
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_5
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a6104fa2617c0ce0800000000000000
      internalID: -1437649512377084250
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_6
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bf15c047ff53f040800000000000000
      internalID: 4680189953316298680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosion 1_7
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56d72099e55849ea0800000000000000
      internalID: -5866917773216416411
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5d0834220be254277b811601206f0cbb
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Explosion 1_0: 6507723212533834303
      Explosion 1_1: 239120857569813423
      Explosion 1_2: 3192016352273360724
      Explosion 1_3: 1781890793491996108
      Explosion 1_4: 6157017127612718517
      Explosion 1_5: -1437649512377084250
      Explosion 1_6: 4680189953316298680
      Explosion 1_7: -5866917773216416411
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
