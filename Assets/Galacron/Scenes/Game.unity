%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: 35f7d26a3aaf94762bfd6306934a1c1e, type: 2}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &24334112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 24334114}
  - component: {fileID: 24334113}
  m_Layer: 0
  m_Name: GameOverState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &24334113
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 24334112}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 46eb1781cfe6407bb5ca0601a86bb7c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &24334114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 24334112}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &55172281
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 55172282}
  - component: {fileID: 55172285}
  - component: {fileID: 55172284}
  - component: {fileID: 55172283}
  m_Layer: 0
  m_Name: FPSCounter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &55172282
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 55172281}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1963437399}
  m_Father: {fileID: 548950671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -50}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 1, y: 0}
--- !u!114 &55172283
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 55172281}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5cd5f1aeef3b4bf499af2fdd08f65c21, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  textMesh: {fileID: 55172284}
  frameTotalAmount: 50
  cooldown: 0.5
--- !u!114 &55172284
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 55172281}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 999
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 50e86f128c57942d4a15196254985f35, type: 2}
  m_sharedMaterial: {fileID: -4096658472320340596, guid: 50e86f128c57942d4a15196254985f35, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 1627389951
  m_fontColor: {r: 1, g: 1, b: 1, a: 0.3764706}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 22
  m_fontSizeBase: 22
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 91.28772, y: 0, z: 7.073934, w: 30.107626}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &55172285
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 55172281}
  m_CullTransparentMesh: 1
--- !u!1 &77510379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 77510380}
  - component: {fileID: 77510381}
  m_Layer: 0
  m_Name: DamageNumberManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &77510380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 77510379}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 863903552}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &77510381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 77510379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6e54c5b28a054ffb8bcbc21c60a5a684, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  damageNumberPrefabs:
  - {fileID: 6881986248500383485, guid: 8f45b8caecec94936977b0f54f3d7481, type: 3}
--- !u!1 &99463065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 99463066}
  - component: {fileID: 99463067}
  - component: {fileID: 99463068}
  m_Layer: 6
  m_Name: Movement
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &99463066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99463065}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 183512956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &99463067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99463065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347d15b0933e4d14877661b4a05e63a7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetTransform: {fileID: 183512956}
  targetRigidbody: {fileID: 183512958}
  fullSpeed: 10
  tapSpeed: 3
--- !u!114 &99463068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 99463065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70d89c4396264a7e9dbf0335f5cbf5b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetTransform: {fileID: 183512956}
  boundOffset: {x: 0.5, y: 0.5}
--- !u!1 &129016609
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 129016611}
  - component: {fileID: 129016610}
  m_Layer: 0
  m_Name: SceneContext
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &129016610
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 129016609}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33979bcb679b4e78b4af882f0f0e7dd6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneInstallers:
  - {fileID: 11400000, guid: cc1cd1151b2e64101b07d4c748421110, type: 2}
--- !u!4 &129016611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 129016609}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &137911943
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 137911944}
  m_Layer: 0
  m_Name: --- GAMEPLAY ------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &137911944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 137911943}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 499702153}
  - {fileID: 2037991594}
  - {fileID: 996866716}
  - {fileID: 183512956}
  - {fileID: 863903552}
  - {fileID: 328256979}
  - {fileID: 755208988}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &183512955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 183512956}
  - component: {fileID: 183512957}
  - component: {fileID: 183512958}
  - component: {fileID: 183512959}
  m_Layer: 6
  m_Name: Player
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &183512956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183512955}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -9.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1535872849}
  - {fileID: 2061739361}
  - {fileID: 99463066}
  - {fileID: 1494359480}
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &183512957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183512955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 365dea464371461fba725abd487c8bf7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inputHandler: {fileID: 2061739363}
  movement: {fileID: 99463067}
  weaponManager: {fileID: 1494359481}
--- !u!50 &183512958
Rigidbody2D:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183512955}
  m_BodyType: 1
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 1
  m_Constraints: 0
--- !u!61 &183512959
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183512955}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: -0.028301522, y: -0.058018208}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.43963072, y: 0.8839636}
  m_EdgeRadius: 0
--- !u!1 &214379542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 214379543}
  m_Layer: 0
  m_Name: --- UI ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &214379543
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 214379542}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 548950671}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &296662795
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 296662796}
  - component: {fileID: 296662798}
  - component: {fileID: 296662797}
  m_Layer: 0
  m_Name: Label
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &296662796
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296662795}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2093543416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &296662797
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296662795}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: LEVEL 99999
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_sharedMaterial: {fileID: 9026158724066135517, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &296662798
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296662795}
  m_CullTransparentMesh: 1
--- !u!1 &313812632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 313812634}
  - component: {fileID: 313812633}
  m_Layer: 0
  m_Name: EvenFormationContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &313812633
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 313812632}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df86b14702574991b8ed842b34b33294, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slotDimensions: {x: 2.2, y: 1.75}
--- !u!4 &313812634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 313812632}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 822783749}
  m_Father: {fileID: 328256979}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &328256978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 328256979}
  - component: {fileID: 328256980}
  - component: {fileID: 328256981}
  m_Layer: 0
  m_Name: FormationController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &328256979
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 328256978}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2125370353}
  - {fileID: 313812634}
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &328256980
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 328256978}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 580f19918dc2475a953d298d6dd3e9e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  motor: {fileID: 328256981}
  evenContainer: {fileID: 313812633}
  oddContainer: {fileID: 2125370352}
  settings: {fileID: 11400000, guid: db2dd368f6de44f268904fde3ea00485, type: 2}
--- !u!114 &328256981
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 328256978}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f9c76420f90426c963a088bf7d04c0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings: {fileID: 11400000, guid: db2dd368f6de44f268904fde3ea00485, type: 2}
  mainCamera: {fileID: 733675570}
  showDebugVisuals: 1
--- !u!1 &342347822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 342347823}
  - component: {fileID: 342347824}
  m_Layer: 0
  m_Name: Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &342347823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 342347822}
  serializedVersion: 2
  m_LocalRotation: {x: 0.8535535, y: 0.35355338, z: 0.35355338, w: 0.1464466}
  m_LocalPosition: {x: 0, y: 0, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2125370353}
  m_LocalEulerAnglesHint: {x: 0, y: 135, z: 135}
--- !u!114 &342347824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 342347822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 3
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.8
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 0
  m_UseNormalMap: 0
  m_ShadowsEnabled: 1
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0.3
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 7.5
  m_PointLightOuterRadius: 10.19
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!1 &409634723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 409634724}
  - component: {fileID: 409634725}
  - component: {fileID: 409634726}
  m_Layer: 0
  m_Name: Score
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &409634724
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 409634723}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 441116998}
  m_Father: {fileID: 548950671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -6.732788}
  m_SizeDelta: {x: -440, y: 25.5935}
  m_Pivot: {x: 0.5, y: 1}
--- !u!114 &409634725
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 409634723}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 252b84554cb7464095d9a7ed499f4eb8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scoreText: {fileID: 441116999}
  spring: {fileID: 409634726}
  scoreFormat: '{0:00000}'
  timePerPoint: 0.025
  maxTime: 1.25
  scaleVelocity: 1.1
  rotationVelocity: 0.5
--- !u!114 &409634726
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 409634723}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da45fff4b609f4611a40147b4e4c9c5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 0
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 1
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 441116998}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!1 &441116997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 441116998}
  - component: {fileID: 441117000}
  - component: {fileID: 441116999}
  m_Layer: 0
  m_Name: ScoreText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &441116998
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 441116997}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 409634724}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &441116999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 441116997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 00000
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_sharedMaterial: {fileID: 9026158724066135517, guid: c08cb6c1602ae4078a305c4327a614f1, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 36
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 20
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &441117000
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 441116997}
  m_CullTransparentMesh: 1
--- !u!1 &499702151
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 499702153}
  - component: {fileID: 499702152}
  m_Layer: 0
  m_Name: VerticalEnemySpawner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &499702152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 499702151}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c3e922dd166e418680f3c3ecbaa4a0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  verticalEnemyPrefab: {fileID: 6701431177337574809, guid: d4323ab77015940feb3216ea14a32c6e, type: 3}
  minSpawnDelay: 2
  maxSpawnDelay: 8
  spawnDelayDecreasePerLevel: 0.3
  minSpawnDelayLimit: 1
  maxEnemiesPerLevel: 1000
  enemiesPerLevelIncrement: 2
  topOffset: 1
--- !u!4 &499702153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 499702151}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &548950670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 548950671}
  - component: {fileID: 548950674}
  - component: {fileID: 548950673}
  - component: {fileID: 548950672}
  m_Layer: 0
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &548950671
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 548950670}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2093543416}
  - {fileID: 55172282}
  - {fileID: 409634724}
  m_Father: {fileID: 214379543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &548950672
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 548950670}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &548950673
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 548950670}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 640, y: 360}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &548950674
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 548950670}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &601882222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 601882225}
  - component: {fileID: 601882224}
  - component: {fileID: 601882223}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &601882223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601882222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
  m_ScrollDeltaPerTick: 6
--- !u!114 &601882224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601882222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &601882225
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601882222}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1431301753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &664973116 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
  m_PrefabInstance: {fileID: 4154967998070256022}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &688972866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 688972867}
  - component: {fileID: 688972868}
  m_Layer: 6
  m_Name: Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &688972867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 688972866}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1535872849}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &688972868
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 688972866}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d6f52c0de29274cc895fade2cb6ee3d4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: a9e7bf73d6c4547559f331bcdff24ead, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &733675568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 733675571}
  - component: {fileID: 733675570}
  - component: {fileID: 733675569}
  - component: {fileID: 733675572}
  - component: {fileID: 733675573}
  - component: {fileID: 733675574}
  - component: {fileID: 733675575}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &733675569
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
--- !u!20 &733675570
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.16630435, g: 0.18293478, b: 0.255, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 11.25
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &733675571
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &733675572
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 1
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!114 &733675573
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c88f5cead0c0b2a4eb05b5900433f8d1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 1
  m_AssetsPPU: 16
  m_RefResolutionX: 640
  m_RefResolutionY: 360
  m_CropFrame: 0
  m_GridSnapping: 0
  m_FilterMode: 0
  m_UpscaleRT: 0
  m_PixelSnapping: 0
  m_CropFrameX: 0
  m_CropFrameY: 0
  m_StretchFill: 0
--- !u!114 &733675574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d71bf759f79b45c685d3e4fd15fa24c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  baseShakeStrength: 5
  shakeOnlyX: 0
  horizontalSpaceshipMultiplier: 1.5
  bulletHitShakeStrength: 0.1
  springForce: 30
  springDrag: 5
  cameraSpring: {fileID: 0}
--- !u!114 &733675575
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 733675568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7646a966235b403698608cffaec33e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 1
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  isValidSpringComponent: 0
  errorReason: 
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  doFixedUpdateRate: 0
  springFixedTimeStep: 0
  initialized: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 50
    commonDrag: 20
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 0
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    cachedPhysicsParameters:
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    physicsParametersDirty: 1
    showDebugFields: 0
    unfolded: 1
    eventsEnabled: 0
    springEvents:
      targetReachedEventEnabled: 0
      clampingEventEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 50
    commonDrag: 20
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    cachedPhysicsParameters:
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    physicsParametersDirty: 1
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    springEvents:
      targetReachedEventEnabled: 0
      clampingEventEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 50
    commonDrag: 20
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 0
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      unclampedCurrentValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    cachedPhysicsParameters:
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    - force: 150
      drag: 10
      integrationParameters:
        forceThreshold: 7500
        useFixedUpdateRate: 1
        fixedTimeStep: 0.02
        alwaysUseAnalyticalSolution: 0
      clampingParameters:
        clampTarget: 1
        clampCurrentValue: 1
        stopSpringOnCurrentValueClamp: 0
        minValue: 0
        maxValue: 100
    physicsParametersDirty: 1
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    springEvents:
      targetReachedEventEnabled: 0
      clampingEventEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 733675571}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
  positionTarget: {x: 0, y: 0, z: 0}
  scaleTarget: {x: 0, y: 0, z: 0}
  rotationTarget: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &755208987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 755208988}
  - component: {fileID: 755208989}
  m_Layer: 0
  m_Name: HorizontalSpawner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &755208988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755208987}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &755208989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755208987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 540a48a4becbb4e678c6f96fab4e16a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  spaceshipPrefab: {fileID: 6047376629506448911, guid: e07e4a047295c40feab7a26961b1adca, type: 3}
  minSpawnDelay: 5
  maxSpawnDelay: 15
  spawnDelayDecreasePerLevel: 0.5
  minSpawnDelayLimit: 3
--- !u!1 &822783748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 822783749}
  - component: {fileID: 822783750}
  m_Layer: 0
  m_Name: Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &822783749
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 822783748}
  serializedVersion: 2
  m_LocalRotation: {x: 0.8535535, y: 0.35355338, z: 0.35355338, w: 0.1464466}
  m_LocalPosition: {x: 0, y: 0, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 313812634}
  m_LocalEulerAnglesHint: {x: 0, y: 135, z: 135}
--- !u!114 &822783750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 822783748}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 3
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.8
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 0
  m_UseNormalMap: 0
  m_ShadowsEnabled: 1
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0.3
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 7.5
  m_PointLightOuterRadius: 10.19
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!1 &828127525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 828127526}
  - component: {fileID: 828127527}
  m_Layer: 0
  m_Name: Global Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &828127526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 828127525}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.92387956, w: 0.38268343}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1667344826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 135}
--- !u!114 &828127527
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 828127525}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 4
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.5
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 2
  m_UseNormalMap: 0
  m_ShadowsEnabled: 0
  m_ShadowIntensity: 0.75
  m_ShadowSoftness: 0
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0
  m_PointLightOuterRadius: 1
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!1 &835259707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 835259708}
  - component: {fileID: 835259709}
  m_Layer: 0
  m_Name: PresentState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &835259708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835259707}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &835259709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835259707}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 986e5d74df6145f384134dcfb7faadfb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  presentationTime: 3
--- !u!1 &863903551
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 863903552}
  m_Layer: 0
  m_Name: DestructionReactors
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &863903552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 863903551}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1790137754}
  - {fileID: 77510380}
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &962852052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 962852053}
  - component: {fileID: 962852054}
  m_Layer: 0
  m_Name: GameInit
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &962852053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 962852052}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &962852054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 962852052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c02d88bb0f4460a8095f2327d7122b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  waitBeforeStart: 1
--- !u!1 &996866715
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 996866716}
  - component: {fileID: 996866717}
  m_Layer: 0
  m_Name: SpawnerController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &996866716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 996866715}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &996866717
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 996866715}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 752579ce23c846fe858ac95d47ee93b1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings: {fileID: 11400000, guid: db2dd368f6de44f268904fde3ea00485, type: 2}
  debug: 0
--- !u!1 &1027945654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1027945656}
  - component: {fileID: 1027945655}
  m_Layer: 0
  m_Name: FSM
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1027945655
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027945654}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8aa7a387c9e434cc6a028733047f2a93, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debug: 1
--- !u!4 &1027945656
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027945654}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 962852053}
  - {fileID: 835259708}
  - {fileID: 1045749770}
  - {fileID: 1086073004}
  - {fileID: 1804339116}
  - {fileID: 24334114}
  m_Father: {fileID: 2037991594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1045749769
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1045749770}
  - component: {fileID: 1045749771}
  m_Layer: 0
  m_Name: PrepareFormationState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1045749770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045749769}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1045749771
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045749769}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 53d1705ea950484788387be90f2215a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  formationController: {fileID: 328256980}
--- !u!1 &1061869216
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1061869218}
  - component: {fileID: 1061869217}
  m_Layer: 0
  m_Name: Swiper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1061869217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1061869216}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ed53003187f74e789acec393519395f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  swipeDamage: 1
  minSwipeLength: 0.1
  swipeWidth: 0.2
  maxSwipePoints: 100
  pointSpacing: 0.05
  enemyLayers:
    serializedVersion: 2
    m_Bits: 256
  swipeLineRenderer: {fileID: 1892042416}
  lineStartWidth: 0.01
  lineEndWidth: 0.25
  lineStartColor: {r: 1, g: 0.9527312, b: 0, a: 1}
  lineEndColor: {r: 0, g: 0.1576705, b: 1, a: 0}
  lineDuration: 0.15
  hitEffectPrefab: {fileID: 0}
  swipeSound: {fileID: 0}
  hitSound: {fileID: 0}
--- !u!4 &1061869218
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1061869216}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1892042417}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1086073003
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1086073004}
  - component: {fileID: 1086073005}
  m_Layer: 0
  m_Name: PlayFormationState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1086073004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1086073003}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1086073005
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1086073003}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a73c6650b60a451fa3ccc6d7ada4c339, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  formationController: {fileID: 328256980}
  spawnerController: {fileID: 996866717}
--- !u!1 &1134781412
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1134781413}
  - component: {fileID: 1134781414}
  m_Layer: 6
  m_Name: Light 2D
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1134781413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134781412}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1535872849}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1134781414
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1134781412}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 3
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.345
  m_Color: {r: 0.9811321, g: 0.9811321, b: 0.9811321, a: 1}
  m_Intensity: 1.2
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 3
  m_NormalMapQuality: 0
  m_UseNormalMap: 0
  m_ShadowsEnabled: 1
  m_ShadowIntensity: 0.76
  m_ShadowSoftness: 0.3
  m_ShadowSoftnessFalloffIntensity: 0.52
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0.5
  m_PointLightOuterRadius: 2.88
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!1 &1221685999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1221686000}
  - component: {fileID: 1221686001}
  m_Layer: 6
  m_Name: Light 2D_001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1221686000
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1221685999}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -1.04, z: 0.14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1535872849}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1221686001
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1221685999}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 073797afb82c5a1438f328866b10b3f0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ComponentVersion: 2
  m_LightType: 3
  m_BlendStyleIndex: 0
  m_FalloffIntensity: 0.345
  m_Color: {r: 0.9529412, g: 0.8978594, b: 0.3176471, a: 1}
  m_Intensity: 1.2
  m_LightVolumeIntensity: 1
  m_LightVolumeEnabled: 0
  m_ApplyToSortingLayers: 00000000
  m_LightCookieSprite: {fileID: 0}
  m_DeprecatedPointLightCookieSprite: {fileID: 0}
  m_LightOrder: 0
  m_AlphaBlendOnOverlap: 0
  m_OverlapOperation: 0
  m_NormalMapDistance: 2
  m_NormalMapQuality: 0
  m_UseNormalMap: 0
  m_ShadowsEnabled: 1
  m_ShadowIntensity: 0.745
  m_ShadowSoftness: 0.3
  m_ShadowSoftnessFalloffIntensity: 0.5
  m_ShadowVolumeIntensityEnabled: 0
  m_ShadowVolumeIntensity: 0.75
  m_LocalBounds:
    m_Center: {x: 0, y: -0.00000011920929, z: 0}
    m_Extent: {x: 0.9985302, y: 0.99853027, z: 0}
  m_PointLightInnerAngle: 360
  m_PointLightOuterAngle: 360
  m_PointLightInnerRadius: 0.5
  m_PointLightOuterRadius: 1.44
  m_ShapeLightParametricSides: 5
  m_ShapeLightParametricAngleOffset: 0
  m_ShapeLightParametricRadius: 1
  m_ShapeLightFalloffSize: 0.5
  m_ShapeLightFalloffOffset: {x: 0, y: 0}
  m_ShapePath:
  - {x: -0.5, y: -0.5, z: 0}
  - {x: 0.5, y: -0.5, z: 0}
  - {x: 0.5, y: 0.5, z: 0}
  - {x: -0.5, y: 0.5, z: 0}
--- !u!1 &1431301752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1431301753}
  m_Layer: 0
  m_Name: --- SERVICES ------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1431301753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431301752}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1845243052}
  - {fileID: 601882225}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1494359479
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1494359480}
  - component: {fileID: 1494359481}
  m_Layer: 6
  m_Name: WeaponManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1494359480
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494359479}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1548603887}
  m_Father: {fileID: 183512956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1494359481
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494359479}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 49f9da276d4343849facb99cff1e2ce5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  availableWeapons:
  - {fileID: 11400000, guid: 29a155687124b45e59e821219bc437e7, type: 2}
  weaponMount: {fileID: 1548603887}
  switchCooldown: 0.5
  initialPrimaryWeapon: {fileID: 11400000, guid: 29a155687124b45e59e821219bc437e7, type: 2}
  initialSecondaryWeapon: {fileID: 0}
--- !u!1 &1535872848
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1535872849}
  m_Layer: 6
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1535872849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1535872848}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 688972867}
  - {fileID: 1134781413}
  - {fileID: 1221686000}
  m_Father: {fileID: 183512956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1548603886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1548603887}
  m_Layer: 6
  m_Name: Mount
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1548603887
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548603886}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.025, y: 0.345, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1494359480}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1554778400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1554778401}
  - component: {fileID: 1554778402}
  m_Layer: 0
  m_Name: Global Volume
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1554778401
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1554778400}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1667344826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1554778402
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1554778400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 172515602e62fb746b5d573b38a5fe58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsGlobal: 1
  priority: 0
  blendDistance: 0
  weight: 1
  sharedProfile: {fileID: 11400000, guid: 3f9215ea0144899419cfbc0957140d3f, type: 2}
--- !u!1 &1667344825
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1667344826}
  m_Layer: 0
  m_Name: --- ENVIRONMENT -------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1667344826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1667344825}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 828127526}
  - {fileID: 1554778401}
  - {fileID: 664973116}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1790137753
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1790137754}
  - component: {fileID: 1790137755}
  m_Layer: 0
  m_Name: ExplosionFormationEnemy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1790137754
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1790137753}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 863903552}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1790137755
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1790137753}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd87e02c20294e678d5ea2e582ae84b1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enemyConfiguration: {fileID: 11400000, guid: b76a8bb1e424c4880bb3226ca2a6bb1f, type: 2}
--- !u!1 &1804339115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1804339116}
  - component: {fileID: 1804339117}
  m_Layer: 0
  m_Name: LevelFormationEndState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1804339116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804339115}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1027945656}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1804339117
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804339115}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df0b523d4457486b9100b95ae0d8ef34, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1845243049
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1845243052}
  - component: {fileID: 1845243051}
  - component: {fileID: 1845243050}
  m_Layer: 0
  m_Name: InputMapSetter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1845243050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1845243049}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 529f9b2bb7594370b1955eeb92360612, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerInput: {fileID: 1845243051}
  inputActionMap: Player
--- !u!114 &1845243051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1845243049}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62899f850307741f2a39c98a8b639597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Actions: {fileID: -944628639613478452, guid: 217640d17deda40218592d9f56e3cc08, type: 3}
  m_NotificationBehavior: 0
  m_UIInputModule: {fileID: 0}
  m_DeviceLostEvent:
    m_PersistentCalls:
      m_Calls: []
  m_DeviceRegainedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ControlsChangedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ActionEvents: []
  m_NeverAutoSwitchControlSchemes: 0
  m_DefaultControlScheme: 
  m_DefaultActionMap: Player
  m_SplitScreenIndex: -1
  m_Camera: {fileID: 0}
--- !u!4 &1845243052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1845243049}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 960, y: 25, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1431301753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1892042415
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1892042417}
  - component: {fileID: 1892042416}
  m_Layer: 0
  m_Name: LineRenderer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!120 &1892042416
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892042415}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: 0, y: 0, z: 0}
  - {x: 0, y: 0, z: 1}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 1
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.5062504
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.9916666
        value: 0.16874999
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 1
--- !u!4 &1892042417
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892042415}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1061869218}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1963437398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1963437399}
  - component: {fileID: 1963437402}
  - component: {fileID: 1963437401}
  m_Layer: 0
  m_Name: FPSLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1963437399
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1963437398}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 55172282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -64.2}
  m_SizeDelta: {x: 200, y: 50}
  m_Pivot: {x: 1, y: 0}
--- !u!114 &1963437401
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1963437398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: FPS
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 50e86f128c57942d4a15196254985f35, type: 2}
  m_sharedMaterial: {fileID: -4096658472320340596, guid: 50e86f128c57942d4a15196254985f35, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 1627389951
  m_fontColor: {r: 1, g: 1, b: 1, a: 0.3764706}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 16
  m_fontSizeBase: 16
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 4
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 91.28772, y: 0, z: 7.073934, w: 30.107626}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1963437402
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1963437398}
  m_CullTransparentMesh: 1
--- !u!1 &2037991592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2037991594}
  - component: {fileID: 2037991593}
  m_Layer: 0
  m_Name: LevelManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2037991593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037991592}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 16cc7b47f5b649309a478b1b8cb61505, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stateMachine: {fileID: 1027945655}
  initialState: {fileID: 962852054}
  states:
  - {fileID: 962852054}
  - {fileID: 835259709}
  - {fileID: 1045749771}
  - {fileID: 1086073005}
  - {fileID: 1804339117}
  - {fileID: 24334113}
  settings: {fileID: 11400000, guid: db2dd368f6de44f268904fde3ea00485, type: 2}
--- !u!4 &2037991594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037991592}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1027945656}
  m_Father: {fileID: 137911944}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2061739360
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2061739361}
  - component: {fileID: 2061739363}
  - component: {fileID: 2061739362}
  m_Layer: 6
  m_Name: InputHandler
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2061739361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061739360}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 183512956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2061739362
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061739360}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62899f850307741f2a39c98a8b639597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Actions: {fileID: -944628639613478452, guid: 217640d17deda40218592d9f56e3cc08, type: 3}
  m_NotificationBehavior: 0
  m_UIInputModule: {fileID: 0}
  m_DeviceLostEvent:
    m_PersistentCalls:
      m_Calls: []
  m_DeviceRegainedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ControlsChangedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ActionEvents: []
  m_NeverAutoSwitchControlSchemes: 0
  m_DefaultControlScheme: 
  m_DefaultActionMap: Player
  m_SplitScreenIndex: -1
  m_Camera: {fileID: 0}
--- !u!114 &2061739363
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061739360}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3a12c1a0fd6f487a8b054dff4aa7082f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  input: {fileID: 2061739362}
  holdThreshold: 0.1
  <InputMovementInfo>k__BackingField:
    direction: {x: 0, y: 0}
    isHolding: 0
--- !u!1 &2093543415
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2093543416}
  - component: {fileID: 2093543419}
  - component: {fileID: 2093543418}
  - component: {fileID: 2093543417}
  m_Layer: 0
  m_Name: LevelInfo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2093543416
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2093543415}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 296662796}
  m_Father: {fileID: 548950671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -115}
  m_SizeDelta: {x: -540, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2093543417
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2093543415}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06bd21fe6c9d4e2192662b041fa61631, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  displayTime: 1
  levelText: {fileID: 296662797}
  fadeTransition: {fileID: 2093543418}
--- !u!114 &2093543418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2093543415}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a2240913d5d0416d9fd41cda37678a3c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  <Duration>k__BackingField: 0.25
  canvasGroup: {fileID: 2093543419}
--- !u!225 &2093543419
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2093543415}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 0
  m_BlocksRaycasts: 0
  m_IgnoreParentGroups: 0
--- !u!1 &2125370351
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2125370353}
  - component: {fileID: 2125370352}
  m_Layer: 0
  m_Name: OddFormationContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2125370352
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2125370351}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df86b14702574991b8ed842b34b33294, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slotDimensions: {x: 2.2, y: 1.75}
--- !u!4 &2125370353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2125370351}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 342347823}
  m_Father: {fileID: 328256979}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &4154967998070256022
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1667344826}
    m_Modifications:
    - target: {fileID: 2084812401041041485, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_Name
      value: Starfield
      objectReference: {fileID: 0}
    - target: {fileID: 5000754364574667896, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: InitialModule.startColor.maxColor.b
      value: 0.8396226
      objectReference: {fileID: 0}
    - target: {fileID: 5000754364574667896, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: InitialModule.startColor.maxColor.g
      value: 0.6855409
      objectReference: {fileID: 0}
    - target: {fileID: 5000754364574667896, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: InitialModule.startColor.maxColor.r
      value: 0.6693218
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5292270664354312152, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 7911074afd07c43e58a069c22d1b1cec, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 733675571}
  - {fileID: 1667344826}
  - {fileID: 214379543}
  - {fileID: 1431301753}
  - {fileID: 137911944}
  - {fileID: 129016611}
  - {fileID: 1061869218}
