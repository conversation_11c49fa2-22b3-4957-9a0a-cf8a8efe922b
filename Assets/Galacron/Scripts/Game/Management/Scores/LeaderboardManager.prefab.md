# Leaderboard Manager Prefab Setup

To create the LeaderboardManager prefab:

1. Create a new empty GameObject in the scene
2. Name it "LeaderboardManager"
3. Add the LeaderboardManager component to it
4. Configure the settings:
   - Max Entries: 10 (or your preferred number)
   - Default Player Name: "Player" (or your preferred default)
5. Create a prefab by dragging the GameObject to the Assets/Galacron/Prefabs/Services/Scenes/Game folder
6. Add the prefab to the GameInstaller asset in the leaderboardManagerPrefab field

# Leaderboard UI Setup

To set up the leaderboard UI:

## 1. Leaderboard Entry Prefab

1. Create a new UI GameObject with a Horizontal Layout Group
2. Add child Text elements for:
   - Rank
   - Player Name
   - Score
   - Level
   - Date
3. Add the LeaderboardEntryUI component to the root GameObject
4. Assign the Text components to the appropriate fields
5. Create a prefab by dragging the GameObject to the Assets/Galacron/Prefabs/UI folder

## 2. Leaderboard Panel

1. Create a new UI Panel GameObject
2. Add a Vertical Layout Group to it
3. Add a ScrollRect with a content container for the entries
4. Add a close button
5. Add the LeaderboardPanel component to the root GameObject
6. Assign the references:
   - Leaderboard Entry Prefab: The prefab created in step 1
   - Entries Container: The content container of the ScrollRect
   - Close Button: The close button
   - Canvas Group: Add a CanvasGroup component and assign it

## 3. New Highscore Panel

1. Create a new UI Panel GameObject
2. Add the following elements:
   - Title text ("New Highscore!")
   - Score display
   - Level display
   - Name input field
   - Submit button
3. Add the NewHighscorePanel component to the root GameObject
4. Assign the references:
   - Canvas Group: Add a CanvasGroup component and assign it
   - Name Input Field: The TMP_InputField for the player's name
   - Score Text: The TextMeshProUGUI for displaying the score
   - Level Text: The TextMeshProUGUI for displaying the level
   - Submit Button: The button for submitting the highscore

## 4. Game Over Panel

1. Create a new UI Panel GameObject
2. Add the following elements:
   - "Game Over" title text
   - Score display
   - Level display
   - Main Menu button
   - Restart button
   - Leaderboard button
3. Add the GameOverPanel component to the root GameObject
4. Assign the references:
   - Canvas Group: Add a CanvasGroup component and assign it
   - Score Text: The TextMeshProUGUI for displaying the score
   - Level Text: The TextMeshProUGUI for displaying the level
   - Main Menu Button: The button for returning to the main menu
   - Restart Button: The button for restarting the game
   - Leaderboard Button: The button for showing the leaderboard
   - Leaderboard Panel: Reference to the LeaderboardPanel created earlier
5. Configure the settings:
   - Main Menu Scene Name: "MainMenu" (or your main menu scene name)
   - Game Scene Name: "Game" (or your game scene name)

Add all these UI elements to your game scene and make sure they are properly connected.
