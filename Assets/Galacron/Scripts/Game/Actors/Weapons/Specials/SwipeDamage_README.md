# Swipe Damage System Setup

This document provides instructions for setting up the Swipe Damage system in the Galacron game.

## Overview

The Swipe Damage system allows players to swipe across the screen with the mouse to damage enemies. When the player swipes over enemies, they take damage based on the configured settings.

## Setup Instructions

### 1. Create the SwipeDamageController Prefab

1. Create a new empty GameObject in the scene
2. Name it "SwipeDamageController"
3. Add the following components:
   - SwipeDamageController script
   - LineRenderer
   - AudioSource (optional)
4. Configure the SwipeDamageController component:
   - Swipe Damage: Set the amount of damage each swipe applies (default: 1)
   - Min Swipe Length: Minimum length required for a valid swipe (default: 0.1)
   - Swipe Width: Width of the swipe collision detection (default: 0.2)
   - Max Swipe Points: Maximum number of points to track in a swipe (default: 100)
   - Point Spacing: Minimum distance between swipe points (default: 0.05)
   - Enemy Layers: Set to the layers containing enemy objects
5. Configure the LineRenderer component:
   - Material: Use a suitable material (e.g., "Sprites/Default")
   - Start Width: Width at the start of the line (default: 0.1)
   - End Width: Width at the end of the line (default: 0.05)
   - Start Color: Color at the start of the line (default: white)
   - End Color: Color at the end of the line (default: transparent white)
6. Create a prefab by dragging the GameObject to the Assets/Galacron/Prefabs/Weapons/Specials folder

### 2. Create the SwipeDamageWeapon Scriptable Object

1. In the Project window, right-click and select Create > Galacron > Weapons > Swipe Damage Weapon
2. Name it "SwipeDamageWeapon"
3. Configure the weapon:
   - Damage: Set to match the SwipeDamageController's swipe damage
   - Cooldown: Time between swipes (default: 0.5)
   - Swipe Controller Prefab: Assign the SwipeDamageController prefab created earlier
4. Save the scriptable object to Assets/Galacron/ScriptableObjects/Weapons

### 3. Create a Hit Effect Prefab (Optional)

1. Create a new empty GameObject
2. Add a Particle System component for the hit effect
3. Configure the Particle System to create a suitable hit effect
4. Create a prefab by dragging the GameObject to the Assets/Galacron/Prefabs/Effects folder
5. Assign this prefab to the Hit Effect Prefab field in the SwipeDamageController

### 4. Add Sound Effects (Optional)

1. Import swipe and hit sound effects to the Assets/Galacron/Audio folder
2. Assign these audio clips to the Swipe Sound and Hit Sound fields in the SwipeDamageController

### 5. Add the Weapon to the Player's Weapon Manager

1. Find the player's WeaponManager component in the scene
2. Add the SwipeDamageWeapon to the available weapons list
3. Configure a key or button to activate the swipe weapon

## Usage

- Left-click and drag across the screen to create a swipe
- Enemies touched by the swipe will take damage
- The swipe will be visualized with a line that fades out after use

## Customization

The swipe system can be customized through the following parameters:

- **Swipe Damage**: How much damage each swipe applies to enemies
- **Min Swipe Length**: The minimum length required for a valid swipe
- **Swipe Width**: The width of the swipe collision detection
- **Line Appearance**: Customize the width, color, and duration of the swipe line
- **Effects**: Add custom hit effects and sounds for feedback

## Troubleshooting

- If enemies aren't being detected, check that they are on the correct layer and that layer is included in the Enemy Layers mask
- If the line renderer isn't visible, check that it has a valid material assigned
- If the swipe isn't working, ensure that the Input System is properly configured for mouse input
