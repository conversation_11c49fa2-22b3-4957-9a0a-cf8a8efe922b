# Vertical Enemy Implementation

## Overview

This implementation adds enemies that enter from the top of the screen and move downward in the Galacron game. The enemies can have different movement patterns and are spawned in the areas to the left or right of the current formation.

## Files Created

1. **VerticalEnemyMovementPattern.cs**
   - Enum defining the different movement patterns for vertical enemies
   - Includes Straight, Sinusoidal, and StopAndGo patterns

2. **VerticalEnemy.cs**
   - Main script for the vertical enemy behavior
   - Handles movement, collision detection, and destruction
   - Implements the IDamageable interface
   - Supports different movement patterns

3. **VerticalEnemySpawner.cs**
   - Manages the spawning of vertical enemies
   - Controls timing based on the current level
   - Calculates spawn positions based on formation boundaries
   - Integrates with the game's event system

## Integration with Existing Systems

### Object Pooling

The vertical enemy system uses the existing object pooling system to efficiently reuse enemy objects:

```csharp
// In VerticalEnemySpawner.cs
GameObject enemy;
if (_poolManager != null)
{
    enemy = _poolManager.GetPooledObject(verticalEnemyPrefab);
}
else
{
    enemy = Instantiate(verticalEnemyPrefab);
}
```

### Event System

The spawner integrates with the game's event system to respond to game state changes:

```csharp
// In VerticalEnemySpawner.cs
[EventListener]
private void OnLevelStarted(LevelStartedEvent evt)
{
    _currentLevel = evt.Level;
    _canSpawn = false;
    _enemiesSpawned = 0;
    _maxEnemiesForCurrentLevel = maxEnemiesPerLevel + (_currentLevel * enemiesPerLevelIncrement);
}

[EventListener]
private void OnSpawnComplete(SpawnCompleteEvent evt)
{
    if (_canSpawn) return;
    
    _canSpawn = true;
    StartSpawning();
}
```

### Damage System

The vertical enemy implements the IDamageable interface to integrate with the existing damage system:

```csharp
// In VerticalEnemy.cs
public void TakeDamage(float damage)
{
    health -= damage;
    if (health <= 0)
    {
        Die();
    }
}
```

## How It Works

1. When a new level starts, the VerticalEnemySpawner is notified through the game's event system.
2. After the formation enemies are spawned (SpawnCompleteEvent), the spawner starts its spawn routine.
3. The spawner calculates the boundaries of the current formation and determines spawn positions in the areas to the left or right of the formation.
4. The spawner waits for a random time (based on the current level) before spawning an enemy.
5. When an enemy is spawned, it is assigned a random movement pattern.
6. The enemy moves downward according to its movement pattern:
   - Straight: Moves directly downward
   - Sinusoidal: Moves downward with a sine wave horizontal movement
   - StopAndGo: Moves downward, stops for a few seconds, then continues
7. When the enemy reaches the bottom of the screen, it is returned to the pool.
8. The player can shoot and destroy the enemy for points.
9. The spawner continues spawning enemies until the maximum number for the current level is reached or the level is completed.

## Movement Patterns

### Straight Movement

The simplest movement pattern, where the enemy moves directly downward:

```csharp
private void MoveStraight()
{
    // Move downward (negative Y direction)
    transform.position += Vector3.down * speed * Time.deltaTime;
}
```

### Sinusoidal Movement

The enemy moves downward while also moving horizontally in a sine wave pattern:

```csharp
private void MoveSinusoidal()
{
    // Calculate horizontal offset using sine wave
    float xOffset = Mathf.Sin(_elapsedTime * frequency) * amplitude;
    
    // Move downward with sinusoidal horizontal movement
    Vector3 newPosition = transform.position;
    newPosition.x = _startX + xOffset;
    newPosition.y -= speed * Time.deltaTime;
    transform.position = newPosition;
}
```

### StopAndGo Movement

The enemy moves downward for a random amount of time, then stops for a random amount of time, then continues:

```csharp
private void MoveStopAndGo()
{
    if (_isStopped)
    {
        // In stopped state
        _stopTimer += Time.deltaTime;
        if (_stopTimer >= _currentStopTime)
        {
            // Resume movement
            _isStopped = false;
            _moveTimer = 0f;
            _currentMoveTime = UnityEngine.Random.Range(minMoveTime, maxMoveTime);
        }
    }
    else
    {
        // In moving state
        _moveTimer += Time.deltaTime;
        
        // Move downward
        transform.position += Vector3.down * speed * Time.deltaTime;
        
        if (_moveTimer >= _currentMoveTime)
        {
            // Stop movement
            _isStopped = true;
            _stopTimer = 0f;
            _currentStopTime = UnityEngine.Random.Range(minStopTime, maxStopTime);
        }
    }
}
```
