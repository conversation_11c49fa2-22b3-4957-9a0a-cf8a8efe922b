# Vertical Enemy Setup

## Overview

The Vertical Enemy system adds enemies that enter from the top of the screen and move downward. These enemies can have different movement patterns:

1. **Straight** - Move directly downward
2. **Sinusoidal** - Move downward with a sine wave horizontal movement
3. **StopAndGo** - Move downward, stop for a few seconds, then continue

The spawner places these enemies in the areas to the left or right of the current formation.

## Creating the Vertical Enemy Prefab

1. Create a new empty GameObject in the scene
2. Name it "VerticalEnemy"
3. Add the following components:
   - SpriteRenderer (assign an enemy sprite)
   - BoxCollider2D (adjust to fit the sprite)
   - Rigidbody2D (set to Kinematic)
   - VerticalEnemy script
   - PooledObject script (from Ludo.Core.Pools.Runtime)

4. Configure the VerticalEnemy component:
   - Set speed to 3
   - Set health to 1
   - Set enemyType to 'V'
   - Set points to 30
   - Choose a movement pattern (Straight, Sinusoidal, or StopAndGo)
   - Configure the movement pattern specific settings
   - Assign an explosion prefab (can use one of the existing explosion prefabs)

5. Create a prefab from this GameObject by dragging it to the Assets/Galacron/Prefabs/Enemies folder

## Setting up the Vertical Enemy Spawner

1. Add the VerticalEnemySpawner component to a GameObject in the scene (can be added to an existing manager GameObject)
2. Assign the VerticalEnemy prefab you created
3. Configure the spawn settings:
   - Min Spawn Delay: 2
   - Max Spawn Delay: 8
   - Spawn Delay Decrease Per Level: 0.3
   - Min Spawn Delay Limit: 1
   - Max Enemies Per Level: 10
   - Enemies Per Level Increment: 2
   - Top Offset: 1

## Adding to Enemy Configurations

1. Open the EnemyConfigurations asset
2. Add a new entry:
   - Enemy Type: 'V'
   - Min Level: 0
   - Max Level: ********** (int.MaxValue)
   - Difficulty: 0.4
   - Enemy Prefab: Assign the VerticalEnemy prefab
   - Explosion Prefab: Assign an explosion prefab

## Testing

1. Play the game
2. After the formation enemies are spawned, vertical enemies should start appearing at the top of the screen
3. They should move downward with their respective movement patterns
4. The player should be able to shoot and destroy them for points
5. When they reach the bottom of the screen, they should disappear and return to the pool

## Customization

The vertical enemy behavior can be customized through the following parameters:

### Basic Settings
- **Speed**: How fast the enemy moves downward
- **Health**: How much damage the enemy can take before being destroyed
- **Points**: How many points the player gets for destroying the enemy

### Sinusoidal Movement
- **Amplitude**: How far the enemy moves horizontally from its center path
- **Frequency**: How quickly the enemy completes a full sine wave cycle

### StopAndGo Movement
- **Min/Max Stop Time**: Range of time the enemy will stop
- **Min/Max Move Time**: Range of time the enemy will move before stopping again

## Integration with Camera Shake

The vertical enemies use the same FormationEnemyKilledEvent as other enemies, so they will automatically trigger camera shake when destroyed.
