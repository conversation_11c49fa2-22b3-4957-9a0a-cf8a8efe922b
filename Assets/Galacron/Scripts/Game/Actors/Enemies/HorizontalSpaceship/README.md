# Horizontal Spaceship Setup

## Creating the Horizontal Spaceship Prefab

1. Create a new empty GameObject in the scene
2. Name it "HorizontalSpaceship"
3. Add the following components:
   - SpriteRenderer (assign a spaceship sprite)
   - BoxCollider2D (adjust to fit the sprite)
   - Rigidbody2D (set to Kinematic)
   - HorizontalSpaceship script
   - PooledObject script (from Ludo.Core.Pools.Runtime)

4. Configure the HorizontalSpaceship component:
   - Set speed to 5
   - Set health to 1
   - Set enemyType to 'S'
   - Set points to 50
   - Assign an explosion prefab (can use one of the existing explosion prefabs)

5. Create a prefab from this GameObject by dragging it to the Assets/Galacron/Prefabs/Enemies folder

## Setting up the Horizontal Spaceship Spawner

1. Add the HorizontalSpaceshipSpawner component to a GameObject in the scene (can be added to an existing manager GameObject)
2. Assign the GameConfiguration asset
3. Assign the HorizontalSpaceship prefab you created
4. Configure the spawn settings:
   - Min Spawn Delay: 5
   - Max Spawn Delay: 15
   - Spawn Delay Decrease Per Level: 0.5
   - Min Spawn Delay Limit: 3

## Adding to Enemy Configurations

1. Open the EnemyConfigurations asset
2. Add a new entry:
   - Enemy Type: 'S'
   - Min Level: 0
   - Max Level: ********** (int.MaxValue)
   - Difficulty: 0.5
   - Enemy Prefab: Assign the HorizontalSpaceship prefab
   - Explosion Prefab: Assign an explosion prefab

## Testing

1. Play the game
2. The horizontal spaceship should appear randomly at the top of the screen after the formation enemies are spawned
3. It should move horizontally across the screen and disappear when it reaches the other side
4. The player should be able to shoot and destroy it for points
