# Horizontal Spaceship Implementation

## Overview

This implementation adds a random spaceship that moves horizontally across the top of the screen in the Galacron game. The spaceship appears at random intervals after the formation enemies are spawned, and moves either from left to right or right to left. When it reaches the opposite side of the screen, it disappears. The game waits a random number of seconds (based on the current level) before spawning another spaceship.

## Files Created

1. **HorizontalSpaceship.cs**
   - Main script for the horizontal spaceship behavior
   - Handles movement, collision detection, and destruction

2. **HorizontalSpaceshipSpawner.cs**
   - Manages the spawning of horizontal spaceships
   - Controls timing based on the current level
   - Integrates with the game's event system

3. **HorizontalSpaceshipTest.cs**
   - Simple test script for verifying the horizontal spaceship functionality
   - Can be used in a test scene to check the behavior

## Integration Steps

### 1. Create the Horizontal Spaceship Prefab

1. Create a new empty GameObject in the scene
2. Name it "HorizontalSpaceship"
3. Add the following components:
   - SpriteRenderer (assign a spaceship sprite)
   - BoxCollider2D (adjust to fit the sprite)
   - Rigidbody2D (set to Kinematic)
   - HorizontalSpaceship script
   - PooledObject script (from Ludo.Core.Pools.Runtime)

4. Configure the HorizontalSpaceship component:
   - Speed: 5
   - Health: 1
   - EnemyType: 'S'
   - Points: 50
   - Assign an explosion prefab (can use one of the existing explosion prefabs)

5. Create a prefab from this GameObject by dragging it to the Assets/Galacron/Prefabs/Enemies folder

### 2. Add to Enemy Configurations

1. Open the EnemyConfigurations asset
2. Add a new entry:
   - Enemy Type: 'S'
   - Min Level: 0
   - Max Level: ********** (int.MaxValue)
   - Difficulty: 0.5
   - Enemy Prefab: Assign the HorizontalSpaceship prefab
   - Explosion Prefab: Assign an explosion prefab

### 3. Add the Spawner to the Game Scene

1. Find an appropriate GameObject in the game scene (e.g., a manager object or the spawner controller)
2. Add the HorizontalSpaceshipSpawner component
3. Configure the component:
   - Assign the GameConfiguration asset
   - Assign the HorizontalSpaceship prefab
   - Configure the spawn settings:
     - Min Spawn Delay: 5
     - Max Spawn Delay: 15
     - Spawn Delay Decrease Per Level: 0.5
     - Min Spawn Delay Limit: 3

## How It Works

1. When a new level starts, the HorizontalSpaceshipSpawner is notified through the game's event system.
2. After the formation enemies are spawned (SpawnCompleteEvent), the spawner starts its spawn routine.
3. The spawner waits for a random time (based on the current level) before spawning a spaceship.
4. The spaceship moves horizontally across the screen and disappears when it reaches the opposite side.
5. The player can shoot and destroy the spaceship for points.
6. When the level is completed (LevelCompleteEvent), the spawner stops spawning spaceships.

## Customization

The horizontal spaceship behavior can be customized through the following parameters:

- **Speed**: How fast the spaceship moves across the screen
- **Health**: How many hits the spaceship can take before being destroyed
- **Points**: How many points the player gets for destroying the spaceship
- **Vertical Position**: Where on the screen the spaceship appears (0.9 = 90% from the bottom)
- **Spawn Delay**: How long to wait between spawning spaceships (decreases with level)

## Testing

You can test the horizontal spaceship functionality using the HorizontalSpaceshipTest script:

1. Create a new scene
2. Add a GameObject with the HorizontalSpaceshipTest script
3. Assign the HorizontalSpaceship prefab
4. Configure the test settings:
   - Spawn Interval: How often to spawn a spaceship
   - Alternate Direction: Whether to alternate between left-to-right and right-to-left

Play the scene to see the spaceships moving across the screen.
