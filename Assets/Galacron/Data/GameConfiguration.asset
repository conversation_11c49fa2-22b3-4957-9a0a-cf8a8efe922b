%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 52b6c5528b1943d19f7ed876423ad987, type: 3}
  m_Name: GameConfiguration
  m_EditorClassIdentifier: 
  enemyConfigurations: {fileID: 11400000, guid: b76a8bb1e424c4880bb3226ca2a6bb1f, type: 2}
  maxRows: 8
  maxColumns: 18
  gridPatterns:
  - <AspectRatioTarget>k__BackingField: 1
    <PaddingPercent>k__BackingField: 0.1
    <MinEnemiesForPadding>k__BackingField: 1
  - <AspectRatioTarget>k__BackingField: 1.3333334
    <PaddingPercent>k__BackingField: 0.1
    <MinEnemiesForPadding>k__BackingField: 12
  - <AspectRatioTarget>k__BackingField: 1.777778
    <PaddingPercent>k__BackingField: 0.1
    <MinEnemiesForPadding>k__BackingField: 14
  baseDifficulty: 10
  difficultyIncrease: 0.25
  dropLevel: 10
  dropRate: 0.9
  minEnemiesPerWave: 1
  maxEnemiesPerWave: 5
  delayBetweenEnemy: 0.25
  waveDelay: 1.5
  lateralSpawners: 5
  verticalOffset: 3
  topSpawners: 5
  horizontalOffset: 3
  numberOfPoints: 10
  swoopWidth: 6
  smoothTension: 1
  baseMovementSpeed: 1
  incrementMovementSpeed: 0.05
  maxMovementSpeed: 5
  boundaryPadding: 1
  movementPatterns: 010000000300000002000000050000000400000006000000
  waveFrequency: 2
  waveAmplitude: 0.5
  circleRadius: 2
