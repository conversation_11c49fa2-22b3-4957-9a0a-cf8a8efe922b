%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &390903203997239739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7058287526867909330}
  - component: {fileID: 6005855599805449652}
  m_Layer: 8
  m_Name: FSM
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7058287526867909330
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 390903203997239739}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 57374591993004386}
  - {fileID: 5971308072056530572}
  - {fileID: 4310025204973543095}
  - {fileID: 6581636115169163392}
  - {fileID: 8634704830739310411}
  m_Father: {fileID: 8002801141096728577}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6005855599805449652
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 390903203997239739}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8aa7a387c9e434cc6a028733047f2a93, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debug: 0
--- !u!1 &703692559775604357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6581636115169163392}
  - component: {fileID: 81811496986131713}
  m_Layer: 8
  m_Name: DiveState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6581636115169163392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 703692559775604357}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7058287526867909330}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &81811496986131713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 703692559775604357}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d1cdaa882284658b3aeb08fa5c5c938, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  canFire: 1
  weaponManager: {fileID: 8208733412415719465}
  fireEveryXSeconds: {x: 0.5, y: 2}
  splineFollower: {fileID: 7945780060702397194}
  splineFollowerData:
    speed: 8
    smoothTime: 0.16
    completionThreshold: 0.99
    rotateTowardsMovement: 1
  numberOfPoints: 4
  swoopWidth: 1
  smoothTension: 1
--- !u!1 &2250641258803510188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5971308072056530572}
  - component: {fileID: 164277568806658865}
  m_Layer: 8
  m_Name: ReturnState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5971308072056530572
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2250641258803510188}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7058287526867909330}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &164277568806658865
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2250641258803510188}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eaa1386fb236473e95342165f7f4ea3f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  canFire: 1
  weaponManager: {fileID: 8208733412415719465}
  fireEveryXSeconds: {x: 0.5, y: 2}
  moveToTarget: {fileID: 4908306375349281625}
  moveToTargetData:
    speed: 7
    rotateToTarget: 1
    target: {fileID: 0}
--- !u!1 &2856686369633712108
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4243443699137614738}
  - component: {fileID: 4971690696190168295}
  m_Layer: 8
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4243443699137614738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2856686369633712108}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8470934092280376205}
  m_Father: {fileID: 8002801141096728577}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4971690696190168295
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2856686369633712108}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da45fff4b609f4611a40147b4e4c9c5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  doesAutoInitialize: 0
  useScaledTime: 1
  alwaysUseAnalyticalSolution: 0
  hasCustomInitialValues: 0
  hasCustomTarget: 0
  generalPropertiesUnfolded: 1
  initialValuesUnfolded: 0
  spaceType: 0
  positionSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  scaleSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
  rotationSpring:
    commonForceAndDrag: 1
    commonForce: 150
    commonDrag: 10
    useInitialValues: 0
    useCustomTarget: 0
    springEnabled: 1
    clampingEnabled: 0
    springValues:
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    - initialValue: 0
      clampTarget: 0
      clampCurrentValue: 0
      stopSpringOnCurrentValueClamp: 0
      minValue: 0
      maxValue: 100
      update: 1
      force: 150
      drag: 10
      target: 0
      currentValue: 0
      candidateValue: 0
      velocity: 0
      clamped: 0
      operationValue: 0
    showDebugFields: 0
    unfolded: 0
    eventsEnabled: 0
    axisRestriction: 0
  followerTransform: {fileID: 4243443699137614738}
  useTransformAsTarget: 0
  targetTransform: {fileID: 0}
--- !u!1 &3166120641484756609
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2114028111258313391}
  - component: {fileID: 8208733412415719465}
  m_Layer: 8
  m_Name: WeaponManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2114028111258313391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3166120641484756609}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8002801141096728577}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8208733412415719465
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3166120641484756609}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 49f9da276d4343849facb99cff1e2ce5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  availableWeapons:
  - {fileID: 11400000, guid: 815d59b5bbffc400dac247ea17e1ee14, type: 2}
  weaponMount: {fileID: 2114028111258313391}
  switchCooldown: 0.5
  initialPrimaryWeapon: {fileID: 11400000, guid: 815d59b5bbffc400dac247ea17e1ee14, type: 2}
  initialSecondaryWeapon: {fileID: 0}
--- !u!1 &5132918779795606963
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8470934092280376205}
  - component: {fileID: 1688832901414038616}
  m_Layer: 8
  m_Name: Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8470934092280376205
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5132918779795606963}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4243443699137614738}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &1688832901414038616
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5132918779795606963}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 82407be3045364a3d93d4f606341fdc1, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &6095585399981068539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8634704830739310411}
  - component: {fileID: 6140242525660542829}
  m_Layer: 8
  m_Name: DieState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8634704830739310411
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6095585399981068539}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7058287526867909330}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6140242525660542829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6095585399981068539}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c87afb8f5c04cd788e6c850192cf6e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  canFire: 0
  weaponManager: {fileID: 8208733412415719465}
  fireEveryXSeconds: {x: 0.5, y: 2}
--- !u!1 &6645231395444198381
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492174429127744671}
  m_Layer: 8
  m_Name: Movement
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492174429127744671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6645231395444198381}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 437740346153384110}
  - {fileID: 2909697573805182083}
  m_Father: {fileID: 8002801141096728577}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7256549828148613492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4310025204973543095}
  - component: {fileID: 6610642506066246943}
  m_Layer: 8
  m_Name: IdleState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4310025204973543095
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7256549828148613492}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7058287526867909330}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6610642506066246943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7256549828148613492}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7aa1802fede2491e95863738f5d2d257, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  canFire: 1
  weaponManager: {fileID: 8208733412415719465}
  fireEveryXSeconds: {x: 0.5, y: 2}
  transformSpring: {fileID: 4971690696190168295}
  springScale: {x: 16, y: 16, z: 16}
  springRotation: {x: 0, y: 0, z: 4}
  diveProbability: 0.05
  minDiveCooldown: 1
  maxDiveCooldown: 3
--- !u!1 &7849666692447662676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8002801141096728577}
  - component: {fileID: 5345399901318768692}
  - component: {fileID: 9141324791781251304}
  - component: {fileID: 6161751756547437936}
  - component: {fileID: 709265349317019458}
  m_Layer: 8
  m_Name: FormationEnemyD
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8002801141096728577
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7849666692447662676}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2114028111258313391}
  - {fileID: 4243443699137614738}
  - {fileID: 7058287526867909330}
  - {fileID: 492174429127744671}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5345399901318768692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7849666692447662676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed5336781df84935a340f406e918c1bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stateMachine: {fileID: 6005855599805449652}
  initialState: {fileID: 9196943528091569063}
  states:
  - {fileID: 9196943528091569063}
  - {fileID: 164277568806658865}
  - {fileID: 6610642506066246943}
  - {fileID: 81811496986131713}
  - {fileID: 6140242525660542829}
--- !u!114 &9141324791781251304
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7849666692447662676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72c08b7010914d6ebc6863088f3666aa, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  health: 1
  enemyType: 68
  points: 10
  enemy: {fileID: 5345399901318768692}
  transformSpring: {fileID: 4971690696190168295}
--- !u!50 &6161751756547437936
Rigidbody2D:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7849666692447662676}
  m_BodyType: 1
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!61 &709265349317019458
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7849666692447662676}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0.0047363043, y: -0.14706662}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 1.2378755, y: 1.065413}
  m_EdgeRadius: 0
--- !u!1 &8076076191654038387
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 57374591993004386}
  - component: {fileID: 9196943528091569063}
  m_Layer: 8
  m_Name: SpawnState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &57374591993004386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8076076191654038387}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7058287526867909330}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9196943528091569063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8076076191654038387}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 972e0cc2c84845819442b1d2acff02ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  canFire: 0
  weaponManager: {fileID: 8208733412415719465}
  fireEveryXSeconds: {x: 0.5, y: 2}
  splineFollower: {fileID: 7945780060702397194}
  splineFollowerData:
    speed: 13.5
    smoothTime: 0.16
    completionThreshold: 0.99
    rotateTowardsMovement: 1
--- !u!1 &8643384407128634497
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2909697573805182083}
  - component: {fileID: 4908306375349281625}
  m_Layer: 8
  m_Name: MoveToTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2909697573805182083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8643384407128634497}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 492174429127744671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4908306375349281625
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8643384407128634497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa9b61caa555482794f043ee6055a7b6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  objectToMove: {fileID: 8002801141096728577}
  data:
    speed: 2
    rotateToTarget: 0
    target: {fileID: 0}
--- !u!1 &8678673452156145647
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 437740346153384110}
  - component: {fileID: 7945780060702397194}
  m_Layer: 8
  m_Name: SplineFollower
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &437740346153384110
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8678673452156145647}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 492174429127744671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7945780060702397194
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8678673452156145647}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 313362218da84a70b4298bde707d80a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  data:
    speed: 7
    smoothTime: 0.16
    completionThreshold: 0.99
    rotateTowardsMovement: 1
  follower: {fileID: 8002801141096728577}
